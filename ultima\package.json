{"name": "ff7-ultima", "private": true, "version": "1.7.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.6", "@react-three/drei": "^9.121.5", "@react-three/fiber": "^8.17.14", "@tauri-apps/api": "^2.2.0", "@tauri-apps/plugin-dialog": "^2.2.0", "@tauri-apps/plugin-fs": "^2.2.0", "@tauri-apps/plugin-global-shortcut": "^2.2.0", "@tauri-apps/plugin-opener": "^2.2.5", "@tauri-apps/plugin-shell": "^2.0.1", "@tauri-apps/plugin-store": "^2.2.0", "@tauri-apps/plugin-updater": "^2.3.0", "@tauri-apps/plugin-window-state": "^2.2.0", "@types/pako": "^2.0.3", "binary-parser": "^1.9.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "color": "^5.0.0", "date-fns": "^3.6.0", "deep-equal": "^2.2.3", "jotai": "^2.12.0", "lodash-es": "^4.17.21", "lucide-react": "^0.468.0", "pako": "^2.1.0", "radix-ui": "^1.4.2", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.173.0", "three-stdlib": "^2.35.13"}, "devDependencies": {"@tauri-apps/cli": "^2.1.0", "@types/color": "^4.2.0", "@types/deep-equal": "^1.0.4", "@types/lodash-es": "^4.17.12", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.0.2", "vite": "^5.0.0"}}