import { atom, useAtom } from 'jotai'
import { useStatusBar } from './useStatusBar'
import { useLgpState } from './useLgpState'
import { EncWFile, EncWData, EncounterSet, EncounterPair, <PERSON><PERSON><PERSON><PERSON>, ChocoboRating } from '@/ff7/encwfile'

interface EncountersState {
  file: EncWFile | null
  data: EncWData | null
}

const encountersStateAtom = atom<EncountersState>({

  file: null,
  data: null,
})

async function readEncW(getFile: (name: string) => Promise<Uint8Array | null>): Promise<Uint8Array> {
  // Try common filename variants just in case
  const candidates = ['enc_w.bin', 'enc_w.BIN', 'ENC_W.BIN']
  for (const name of candidates) {
    const buf = await getFile(name)
    if (buf) return buf
  }
  throw new Error('enc_w.bin not found in world_us.lgp')
}

export function useEncountersState() {
  const [state, setState] = useAtom(encountersStateAtom)
  const { setMessage } = useStatusBar()
  const { getFile, setFile } = useLgpState()

  const loadEncounters = async () => {
    try {
      console.log('Loading encounters...')
      const data = await readEncW(getFile)
      const file = new EncWFile(data)
      setState({ file, data: file.data })
    } catch (error) {
      console.error('[Encounters] Failed to load enc_w.bin', error)
      setMessage('Failed to load encounters: ' + (error as Error).message, true)
    }
  }

  const saveEncounters = async () => {
    try {
      if (!state.file) {
        setMessage('No encounters loaded to save', true)
        return
      }
      const data = state.file.writeFile()
      await setFile('enc_w.bin', data)
      setMessage('Encounters saved successfully!')
    } catch (error) {
      console.error('[Encounters] Failed to save enc_w.bin', error)
      setMessage('Failed to save encounters: ' + (error as Error).message, true)
    }
  }

  const updateYuffie = (index: number, updates: Partial<YuffieEncounter>) => {
    setState(prev => {
      if (!prev.file || !prev.data) return prev

      // Create a deep copy of the data and update it
      const newData = structuredClone(prev.data)
      newData.yuffieEncounters[index] = { ...newData.yuffieEncounters[index], ...updates }

      // Keep underlying file data in sync for straightforward saving
      const updatedEntry = newData.yuffieEncounters[index]
      prev.file.setYuffieEncounter(index, updatedEntry)

      return {
        file: prev.file,
        data: newData
      }
    })
  }

  const updateChocobo = (index: number, updates: Partial<ChocoboRating>) => {
    setState(prev => {
      if (!prev.file || !prev.data) return prev

      // Create a deep copy of the data and update it
      const newData = structuredClone(prev.data)
      newData.chocoboRatings[index] = { ...newData.chocoboRatings[index], ...updates } as ChocoboRating

      // Keep underlying file data in sync for straightforward saving
      const updatedEntry = newData.chocoboRatings[index]
      prev.file.setChocoboRating(index, updatedEntry)

      return {
        file: prev.file,
        data: newData
      }
    })
  }

  const updateEncounterMeta = (
    regionIndex: number,
    setIndex: number,
    updates: Partial<Pick<EncounterSet, 'active' | 'encounterRate'>>
  ) => {
    setState(prev => {
      if (!prev.file || !prev.data) return prev

      // Create a deep copy of the data
      const newData = structuredClone(prev.data)

      // Update the specific encounter set in the new data
      const currentSet = newData.randomEncounters.regions[regionIndex].sets[setIndex]
      const updatedSet = { ...currentSet, ...updates } as EncounterSet
      newData.randomEncounters.regions[regionIndex].sets[setIndex] = updatedSet

      // Keep underlying file data in sync for straightforward saving
      prev.file.setEncounterSet(regionIndex, setIndex, updatedSet)

      return {
        file: prev.file,
        data: newData
      }
    })
  }

  const updateEncounterPair = (
    regionIndex: number,
    setIndex: number,
    group: 'normal' | 'back' | 'side' | 'pincer' | 'chocobo',
    indexInGroup: number | null,
    updates: Partial<EncounterPair>
  ) => {
    setState(prev => {
      if (!prev.file || !prev.data) return prev

      // Create a deep copy of the data
      const newData = structuredClone(prev.data)
      const set = newData.randomEncounters.regions[regionIndex].sets[setIndex]

      if (group === 'normal' && indexInGroup != null) {
        set.normalEncounters[indexInGroup] = { ...set.normalEncounters[indexInGroup], ...updates }
      } else if (group === 'back' && indexInGroup != null) {
        set.backAttacks[indexInGroup] = { ...set.backAttacks[indexInGroup], ...updates }
      } else if (group === 'side') {
        set.sideAttack = { ...set.sideAttack, ...updates }
      } else if (group === 'pincer') {
        set.pincerAttack = { ...set.pincerAttack, ...updates }
      } else if (group === 'chocobo' && indexInGroup != null) {
        set.chocoboEncounters[indexInGroup] = { ...set.chocoboEncounters[indexInGroup], ...updates }
      }

      // Keep underlying file data in sync for straightforward saving
      const updatedSet = newData.randomEncounters.regions[regionIndex].sets[setIndex]
      prev.file.setEncounterSet(regionIndex, setIndex, updatedSet)

      return {
        file: prev.file,
        data: newData
      }
    })
  }

  return {
    data: state.data,
    loadEncounters,
    saveEncounters,
    updateYuffie,
    updateChocobo,
    updateEncounterMeta,
    updateEncounterPair,
  }
}


