* Opcode: '''0x34c'''
* Name: set submarine color
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Type
| Submarine type
|}

==== Notes ====

Sets the color of the submarine.

 0 - red submarine
 1 - blue submarine
 
 as a side effect of how this instruction is implemented it is possible to access the chocobo colors for the submarine
 -1 - gold submarine
 -2 - black submarine
 etc
 this will only change the color of the submarine and does not affect gameplay in any way