* Opcode: '''0x019'''
* Name: push distance from active entity to entity by model id
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Model ID
| Model to compare distance to
|}

==== Notes ====

Pushes the distance between the active entity and the entity corresponding to the model specified. It is unclear exactly how the distance calculation works or what values to expect from this function.