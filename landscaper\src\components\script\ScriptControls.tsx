import { But<PERSON> } from "@/components/ui/button"
import { Too<PERSON>ip<PERSON>rovider, <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>rigger, TooltipContent } from "@/components/ui/tooltip"
import { Plus, Trash2, Search, ChevronLeft, ChevronRight } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MapId } from "@/hooks/useMapState"
import { useScriptsState } from "@/hooks/useScriptState"
import { FunctionType } from "@/ff7/evfile"
import { useState } from "react"
import { AddScriptModal } from "@/components/modals/AddScriptModal"
import { useKeyboardShortcuts, getShortcutDisplay } from "@/hooks/useKeyboardShortcuts"

const MAP_NAMES: Record<MapId, string> = {
  WM0: "Overworld",
  WM2: "Underwater",
  WM3: "Great Glacier",
}

export function ScriptControls() {
  const {
    selectedMap,
    scriptType,
    setSelectedMap,
    setScriptType,
    loadScripts,
    addModelScript,
    addMeshScript,
    canGoBack,
    canGoForward,
    goBack,
    goForward,
  } = useScriptsState()
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)

  // Set up keyboard shortcuts for script navigation
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0
  const shortcuts = [
    {
      key: isMac ? '[' : 'ArrowLeft',
      ctrlOrCmd: isMac,
      alt: !isMac,
      action: () => {
        if (canGoBack()) {
          goBack()
        }
      },
      description: 'Go back to previous script'
    },
    {
      key: isMac ? ']' : 'ArrowRight',
      ctrlOrCmd: isMac,
      alt: !isMac,
      action: () => {
        if (canGoForward()) {
          goForward()
        }
      },
      description: 'Go forward to next script'
    }
  ]

  useKeyboardShortcuts(shortcuts)

  const handleMapChange = (value: MapId) => {
    setSelectedMap(value)
    loadScripts(value)
  }

  const handleScriptTypeChange = (value: string) => {
    setScriptType(Number(value) as FunctionType)
  }

  const handleAddScript = async (params: any) => {
    try {
      if (params.type === "model") {
        await addModelScript(params.modelId, params.functionId)
      } else if (params.type === "mesh") {
        await addMeshScript(params.x, params.y, params.functionId)
      }
    } catch (error) {
      // Error is already handled in the hook
    }
  }

  const canAddScripts = scriptType === FunctionType.Model || scriptType === FunctionType.Mesh

  return (
    <div className="w-full bg-sidebar border-b border-slate-800/40 flex items-center justify-between gap-2 px-2 py-1">
      {/* Left side - Script actions */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-1.5">
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => setIsAddModalOpen(true)}
                  disabled={!canAddScripts}
                >
                  <Plus className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="text-xs">
                <p>Add new script</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" className="h-6 w-6">
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="text-xs">
                <p>Delete selected script</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="h-3.5 w-[1px] bg-border" />

        <div className="flex items-center gap-1.5">
          <span className="text-[10px] text-muted-foreground font-medium">Map:</span>
          <Select value={selectedMap} onValueChange={handleMapChange}>
            <SelectTrigger className="w-[110px] px-2 h-6 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(MAP_NAMES).map(([id, name]) => (
                <SelectItem key={id} value={id}>
                  {name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="h-3.5 w-[1px] bg-border" />

        <div className="flex items-center gap-1.5">
          <span className="text-[10px] text-muted-foreground font-medium">Type:</span>
          <Select value={scriptType.toString()} onValueChange={handleScriptTypeChange}>
            <SelectTrigger className="w-[90px] px-2 h-6 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={FunctionType.System.toString()}>System</SelectItem>
              <SelectItem value={FunctionType.Model.toString()}>Models</SelectItem>
              <SelectItem value={FunctionType.Mesh.toString()}>Mesh</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="h-3.5 w-[1px] bg-border" />

        <div className="flex items-center gap-1.5">
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-6 w-6"
                  onClick={goBack}
                  disabled={!canGoBack()}
                >
                  <ChevronLeft className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="text-xs">
                <p>Go back to previous script ({getShortcutDisplay({ key: isMac ? '[' : 'ArrowLeft', ctrlOrCmd: isMac, alt: !isMac, action: () => {} })})</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-6 w-6"
                  onClick={goForward}
                  disabled={!canGoForward()}
                >
                  <ChevronRight className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="text-xs">
                <p>Go forward to next script ({getShortcutDisplay({ key: isMac ? ']' : 'ArrowRight', ctrlOrCmd: isMac, alt: !isMac, action: () => {} })})</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Right side - Search */}
      <div className="flex items-center gap-1.5">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
          <Input type="search" placeholder="Search scripts..." className="h-6 w-[200px] pl-8 text-xs" />
        </div>
      </div>

      <AddScriptModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        scriptType={scriptType}
        onAddScript={handleAddScript}
      />
    </div>
  )
}
