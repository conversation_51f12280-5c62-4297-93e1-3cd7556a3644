* Opcode: '''0x0e0'''
* Name: write bank
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Bank Address
| Bank location to write to
|-
| Stack
| Value
| Value to write
|}

==== Notes ====

Bank Address should be pushed as a normal bank access instruction, the location that would be accessed will instead be overwritten with the specified value. It is possible to write to a single bit, a byte or a word by changing the bank address to the desired format.