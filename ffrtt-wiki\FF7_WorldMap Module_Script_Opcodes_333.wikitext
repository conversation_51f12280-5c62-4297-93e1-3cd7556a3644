* Opcode: '''0x333'''
* Name: rotate current entity to model
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Model ID
| Model to which current entity model should be rotated towards
|-
| Stack
| Angle offset
| Additional rotation after rotating towards the model, 0 means no rotation, 128 means turning its back to the model
|}

==== Notes ====