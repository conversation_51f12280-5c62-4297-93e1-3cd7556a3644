* Opcode: '''0x100'''
* Name: reset stack
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|}

==== Notes ====

Completely resets the stack. This function was probably introduced to work around bugs in the script engine, there are some issues that can cause items on the stack to remain after an instruction has been executed, otherwise there is no good reason why you would ever need to clear the stack.