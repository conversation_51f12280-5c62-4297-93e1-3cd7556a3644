use serde::{Serialize, Deserialize};

#[derive(<PERSON><PERSON><PERSON>, Deserialize, <PERSON>lone)]
pub struct Light {
    pub color: [u8; 3], // RGB
    pub x: i32,
    pub y: i32,
    pub z: i32,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct FieldLights {
    pub global_light_color: [u8; 3], // RGB
    pub light1: Light,
    pub light2: Light,
    pub light3: Light,
}

#[derive(Serialize)]
pub struct FieldModel {
    pub x: i32,
    pub y: i32,
    pub z: i32,
    pub direction: u8,
    pub triangle: u16,
    pub collision: u8,
    pub interaction: u8,
    pub visible: u8,
    pub lights: FieldLights,
}

#[derive(Serialize)]
pub struct FieldData {
    pub field_id: u16,
    pub field_name: Vec<u8>,
    pub field_model_count: u16,
    pub field_model_names: Vec<String>,
}

#[derive(Serialize)]
pub struct FieldLineObj {
    pub x1: i16,
    pub y1: i16,
    pub z1: i16,
    pub x2: i16,
    pub y2: i16,
    pub z2: i16,
    pub enabled: u8,
    pub entity: u8,
}