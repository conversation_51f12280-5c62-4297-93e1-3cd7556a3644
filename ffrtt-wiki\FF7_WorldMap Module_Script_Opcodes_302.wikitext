* Opcode: '''0x302'''
* Name: set player entity
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|}

==== Notes ====

This instruction will make the current active entity the "player" entity, any instructions that operate on the player entity will now affect this entity instead. Probably affects camera and controls but this has not yet been verified.