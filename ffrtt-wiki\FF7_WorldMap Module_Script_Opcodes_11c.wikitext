* Opcode: '''0x11c'''
* Name: push word from bank0
* Two-word opcode: Yes

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Code
| Address
| Byte address in bank
|}

==== Notes ====

Pushes the word at Address from bank0 (Bank 1 in the [[FF7/Savemap|Savemap]]).
This instruction uses byte addressing but unaligned accesses are not allowed! Address must be a multiple of two.

This is a valid parameter to the [[FF7/WorldMap_Module/Script/Opcodes/0e0|write bank opcode]].