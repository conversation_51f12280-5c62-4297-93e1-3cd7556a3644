{"name": "ff7-landscaper", "private": true, "version": "0.9.2", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest run", "test:watch": "vitest", "extract-scripts": "tsx scripts/extract-world-scripts.ts", "get-unused-locations": "tsx scripts/get-unused-locations.ts"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.7", "@react-three/drei": "^9.121.3", "@react-three/fiber": "^8.17.12", "@tauri-apps/api": "^2.2.0", "@tauri-apps/plugin-dialog": "^2.2.0", "@tauri-apps/plugin-fs": "^2.2.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-updater": "^2.3.1", "@types/lodash-es": "^4.17.12", "ace-builds": "^1.39.0", "binary-parser": "^1.9.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.11.0", "lodash-es": "^4.17.21", "lucide-react": "^0.471.1", "prism-code-editor": "^3.4.0", "prism-react-editor": "^1.1.0", "react": "^18.3.1", "react-ace": "^14.0.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.172.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^22.13.9", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "tsx": "^4.19.3", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^3.0.8"}}