import { useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { RandomEncounters } from "@/components/encounters/RandomEncounters"
import { Yu<PERSON>ieEncounters } from "@/components/encounters/YuffieEncounters"
import { ChocoboEncounters } from "@/components/encounters/ChocoboEncounters"
import { useLgpState } from '@/hooks/useLgpState'
import { useEncountersState } from '@/hooks/useEncountersState'

export function EncountersTab() {
  const { opened, openedTime } = useLgpState()
  const { loadEncounters } = useEncountersState()

  useEffect(() => {
    async function load() {
      if (!opened) return
      await loadEncounters()
    }
    load()
  }, [opened, openedTime])

  return (
    <div className="flex flex-col w-full min-h-0">
      <div className="w-full bg-sidebar border-b border-slate-800/40 px-2 py-2">
        <Tabs defaultValue="random" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="random">Random encounters</TabsTrigger>
            <TabsTrigger value="yuffie">Yuffie encounters</TabsTrigger>
            <TabsTrigger value="chocobo">Chocobo encounters</TabsTrigger>
          </TabsList>
          <TabsContent value="random" className="mt-0">
            <RandomEncounters />
          </TabsContent>
          <TabsContent value="yuffie" className="mt-0">
            <YuffieEncounters />
          </TabsContent>
          <TabsContent value="chocobo" className="mt-0">
            <ChocoboEncounters />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 