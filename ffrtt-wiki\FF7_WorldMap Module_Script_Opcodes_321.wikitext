* Opcode: '''0x321'''
* Name: face point
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Point Index
| Index of the point to face
|}

==== Notes ====

Appears to be used to set the active entity's direction to face a given point number.

Examples:

Used to ensure, when entering the edge of gold saucer desert, that the player goes away from the desert and not towards it (by facing <PERSON> towards it, then rotating him 180 degrees)