[package]
name = "ff7-lib"
version = "1.7.0"
edition = "2021"
authors = ["m4v3r"]
description = "FF7 memory manipulation and data structures library"

[dependencies]
log = "0.4"
thiserror = "1.0"
winapi = { version = "0.3", features = ["memoryapi", "winnt", "winuser", "processthreadsapi", "handleapi"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
sysinfo = "=0.30.12"
process-memory = "0.5.0"
lazy_static = "1.5.0"
parking_lot = "0.12.3" 
flate2 = "1.0"
byteorder = "1.5.0"

[dev-dependencies]
mockall = "0.11.4"
tempfile = "3.6.0"
