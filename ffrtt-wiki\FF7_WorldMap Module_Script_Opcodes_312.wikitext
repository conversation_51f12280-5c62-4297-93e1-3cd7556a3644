* Opcode: '''0x312'''
* Name: set light coordinates in mesh
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| X
| X coordinate in mesh
|-
| Stack
| Z
| Z coordinate in mesh
|}

==== Notes ====

Sets the active point's coordinates within its mesh, X and Z are numbers between 0 and 8191 (the size of one mesh).

World coordinates can be calculated as mesh coordinates << 13 + coordinates in mesh or mesh coordinates << 13 | coordinates in mesh (coordinates in mesh can be thought of as the lower 13 bits while mesh coordinates are the upper bits).