* Opcode: '''0x080'''
* Name: push and
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| A
| Bits
|-
| Stack
| B
| Bits
|}

==== Notes ====

Pushes A & B to the stack.