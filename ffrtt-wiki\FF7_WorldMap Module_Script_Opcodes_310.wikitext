* Opcode: '''0x310'''
* Name: set active point
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Point ID
| Point to set active
|-
| Stack
| Point Type
| Sets the type of this point
|}

==== Notes ====

The specified point is made active, Point ID is a value between 0 and 15 for a total of 16 unique points. All operations on points affect the point which is currently set active by means of this instruction.

Point Type sets a flag on the point specified and seems to determine whether the point is a light source (1) or just a set of coordinates (0).