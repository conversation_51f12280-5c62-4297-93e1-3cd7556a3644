* Opcode: '''0x117''', '''0x11b''' & '''0x11f'''
* Name: push special
* Two-word opcode: Yes

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Code
| Address
| "Address" to the special variable
|}

==== Notes ====

There are 21 different special variables accessible to the script engine. There are several opcodes that access these variables in the same way, presumably they were supposed to be bit, byte and word versions of the instruction but there is absolutely no difference in the way they work.

The variables you can access through this instruction are:

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Address
! style="background:rgb(204,204,204)" align="center" | Description
|-
| 0
| Active entity X mesh coordinate
|-
| 1
| Active entity Y mesh coordinate
|-
| 2
| Active entity X coordinate in mesh
|-
| 3
| Active entity Y coordinate in mesh
|-
| 4
| Active entity direction (0-255)
|-
| 5
| Scenario parameter when entering WM from a field (see [[FF7/WorldMap_Module/Script/Opcodes/318|opcode 0x318]])
|-
| 6
| Field ID of "wm" field that jumped to the WM
|-
| 7
| Unknown
|-
| 8
| Model ID of entity at top of entity list
|-
| 9
| Model ID of active entity
|-
| 10
| Check if ran from battle
|-
| 11
| Zolom escape result flag (1: place party on the stables side, 0: place party on the cave side)
|-
| 12
| Prompt window result
|-
| 13
| Script index of current mesh triangle of active entity
|-
| 14
| Player party member model ID (0=Cloud, 1=Tifa, 2=Cid)
|-
| 15
| Active Entity Model ID
|-
| 16
| Random byte (from World Map RNG)
|-
| 17
| Unknown
|-
| 18
| Unknown
|-
| 19
| Unknown
|-
| 20
| Unknown
|}