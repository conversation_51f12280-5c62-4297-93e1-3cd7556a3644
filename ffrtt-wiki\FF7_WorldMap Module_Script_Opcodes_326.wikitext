* Opcode: '''0x326'''
* Name: set window prompt
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Message
| Dialog message ID
|-
| Stack
| First
| First option message ID
|-
| Stack
| Last
| Last option message ID
|}

==== Notes ====

Works similarly to the ASK field opcode, except there is only one window so no ID is necessary. Dialog is taken from the [[FF7/WorldMap Module/Dialog|mes]] file.

The chosen option is stored into a special variable that can be accessed with the [[FF7/WorldMap Module/Script/Opcodes/117|push special]] instruction.