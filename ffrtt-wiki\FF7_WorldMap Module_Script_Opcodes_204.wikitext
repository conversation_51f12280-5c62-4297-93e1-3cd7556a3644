* Opcode: '''0x204''' & the rest of the 200 series of opcodes
* Name: run function
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Model
| Model ID
|}

==== Notes ====

Runs the function (opcode - 0x204) in the context of the specified model. Opcode 204 runs function 0, opcode 205 runs function 1 etc.

An invalid model ID (>=64) causes it to run a system function in the context of the current active entity. This is used throughout the worldmap scripts where certain model functions call system functions (model ID is then set to 65535, or 0xFFFF in hex).