* Opcode: '''0x018'''
* Name: push distance from active entity to point
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Point ID
| Point to compare distance to
|}

==== Notes ====

Pushes the distance between the active entity and the point specified. It is unclear exactly how the distance calculation works or what values to expect from this function.