* Opcode: '''0x0c0'''
* Name: push logicor
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| A
| Boolean value
|-
| Stack
| B
| Boolean value
|}

==== Notes ====

Pushes 0 to the stack if both A and B are 0, otherwise 1.