
== Stack Operations: Arithmetic ==

 [[FF7/WorldMap_Module/Script/Opcodes/015|015 push neg]]
 [[FF7/WorldMap_Module/Script/Opcodes/017|017 push logicnot]]
 [[FF7/WorldMap_Module/Script/Opcodes/018|018 push distance from active entity to point]]
 [[FF7/WorldMap_Module/Script/Opcodes/019|019 push distance from active entity to entity by model id]]
 [[FF7/WorldMap_Module/Script/Opcodes/01b|01b push direction from active entity to point]]
 [[FF7/WorldMap_Module/Script/Opcodes/030|030 push mul]]
 [[FF7/WorldMap_Module/Script/Opcodes/040|040 push add]]
 [[FF7/WorldMap_Module/Script/Opcodes/041|041 push sub]]
 [[FF7/WorldMap_Module/Script/Opcodes/050|050 push shl]]
 [[FF7/WorldMap_Module/Script/Opcodes/051|051 push shr]]
 [[FF7/WorldMap_Module/Script/Opcodes/060|060 push less]]
 [[FF7/WorldMap_Module/Script/Opcodes/061|061 push greater]]
 [[FF7/WorldMap_Module/Script/Opcodes/062|062 push lessequal]]
 [[FF7/WorldMap_Module/Script/Opcodes/063|063 push greaterequal]]
 [[FF7/WorldMap_Module/Script/Opcodes/070|070 push equal]]
 [[FF7/WorldMap_Module/Script/Opcodes/080|080 push and]]
 [[FF7/WorldMap_Module/Script/Opcodes/0a0|0a0 push or]]
 [[FF7/WorldMap_Module/Script/Opcodes/0b0|0b0 push logicand]]
 [[FF7/WorldMap_Module/Script/Opcodes/0c0|0c0 push logicor]]
 [[FF7/WorldMap_Module/Script/Opcodes/0e0|0e0 write bank]]

== Stack Operations: Data Sources ==

 [[FF7/WorldMap_Module/Script/Opcodes/100|100 reset stack]]
 [[FF7/WorldMap_Module/Script/Opcodes/110|110 push constant]]
 [[FF7/WorldMap_Module/Script/Opcodes/114|114 push bit from bank0]]
 [[FF7/WorldMap_Module/Script/Opcodes/117|117 push special]]
 [[FF7/WorldMap_Module/Script/Opcodes/118|118 push byte from bank0]]
 [[FF7/WorldMap_Module/Script/Opcodes/119|119 push byte from bank1]]
 [[FF7/WorldMap_Module/Script/Opcodes/117|11b push special]]
 [[FF7/WorldMap_Module/Script/Opcodes/11c|11c push word from bank0]]
 [[FF7/WorldMap_Module/Script/Opcodes/11d|11d push word from bank1]]
 [[FF7/WorldMap_Module/Script/Opcodes/117|11f push special]]

== Flow Control ==

 [[FF7/WorldMap_Module/Script/Opcodes/200|200 jump]]
 [[FF7/WorldMap_Module/Script/Opcodes/201|201 jump if false]]
 [[FF7/WorldMap_Module/Script/Opcodes/203|203 return]]
 [[FF7/WorldMap_Module/Script/Opcodes/204|204 run function]]

== System Operations ==

 [[FF7/WorldMap_Module/Script/Opcodes/300|300 load model]]
 [[FF7/WorldMap_Module/Script/Opcodes/302|302 set player entity]]
 [[FF7/WorldMap_Module/Script/Opcodes/303|303 set active entity movespeed]]
 [[FF7/WorldMap_Module/Script/Opcodes/304|304 set active entity direction & facing]]
 [[FF7/WorldMap_Module/Script/Opcodes/305|305 set wait frames]]
 [[FF7/WorldMap_Module/Script/Opcodes/306|306 wait?]]
 [[FF7/WorldMap_Module/Script/Opcodes/307|307 set control lock]]
 [[FF7/WorldMap_Module/Script/Opcodes/308|308 set active entity mesh coordinates]]
 [[FF7/WorldMap_Module/Script/Opcodes/309|309 set active entity coordinates in mesh]]
 [[FF7/WorldMap_Module/Script/Opcodes/30a|30a set active entity vertical speed]]
 [[FF7/WorldMap_Module/Script/Opcodes/30b|30b set active entity y offset]]
 [[FF7/WorldMap_Module/Script/Opcodes/30c|30c enter vehicle?]]
 [[FF7/WorldMap_Module/Script/Opcodes/30d|30d stop entity]]
 [[FF7/WorldMap_Module/Script/Opcodes/30e|30e active entity play animation]]
 [[FF7/WorldMap_Module/Script/Opcodes/310|310 set active point]]
 [[FF7/WorldMap_Module/Script/Opcodes/311|311 set point mesh coordinates]]
 [[FF7/WorldMap_Module/Script/Opcodes/312|312 set point coordinates in mesh]]
 [[FF7/WorldMap_Module/Script/Opcodes/313|313 set point terrain BGR]]
 [[FF7/WorldMap_Module/Script/Opcodes/314|314 set point dropoff parameters]]
 [[FF7/WorldMap_Module/Script/Opcodes/315|315 set point sky top BGR]]
 [[FF7/WorldMap_Module/Script/Opcodes/316|316 set point sky bottom BGR]]
 [[FF7/WorldMap_Module/Script/Opcodes/317|317 trigger battle]]
 [[FF7/WorldMap_Module/Script/Opcodes/318|318 enter field scene]]
 [[FF7/WorldMap_Module/Script/Opcodes/319|319 set map options]]
 [[FF7/WorldMap_Module/Script/Opcodes/31b|31b noop]]
 [[FF7/WorldMap_Module/Script/Opcodes/31c|31c set camera tilt/zoom status]]
 [[FF7/WorldMap_Module/Script/Opcodes/31d|31d play sound effect]]
 [[FF7/WorldMap_Module/Script/Opcodes/31f|31f set camera rotation speed]]
 [[FF7/WorldMap_Module/Script/Opcodes/320|320 reset zolom]]
 [[FF7/WorldMap_Module/Script/Opcodes/321|321 face point]]
 [[FF7/WorldMap_Module/Script/Opcodes/324|324 set window dimensions]]
 [[FF7/WorldMap_Module/Script/Opcodes/325|325 set window message]]
 [[FF7/WorldMap_Module/Script/Opcodes/326|326 set window prompt]]
 [[FF7/WorldMap_Module/Script/Opcodes/327|327 wait for prompt acknowledge?]]
 [[FF7/WorldMap_Module/Script/Opcodes/328|328 set active entity direction]]
 [[FF7/WorldMap_Module/Script/Opcodes/329|329 set camera tilt speed]]
 [[FF7/WorldMap_Module/Script/Opcodes/32a|32a set camera zoom speed]]
 [[FF7/WorldMap_Module/Script/Opcodes/32b|32b set battle lock]]
 [[FF7/WorldMap_Module/Script/Opcodes/32c|32c set window parameters]]
 [[FF7/WorldMap_Module/Script/Opcodes/32d|32d wait for window ready]]
 [[FF7/WorldMap_Module/Script/Opcodes/32e|32e wait for message acknowledge]]
 [[FF7/WorldMap_Module/Script/Opcodes/32f|32f set player direction]]
 [[FF7/WorldMap_Module/Script/Opcodes/330|330 set active entity]]
 [[FF7/WorldMap_Module/Script/Opcodes/331|331 exit vehicle]]
 [[FF7/WorldMap_Module/Script/Opcodes/332|332 chocobo runs away]]
 [[FF7/WorldMap_Module/Script/Opcodes/333|333 rotate current entity to model]]
 [[FF7/WorldMap_Module/Script/Opcodes/334|334 wait for function]]
 [[FF7/WorldMap_Module/Script/Opcodes/336|336 set active entity movespeed (honor walkmesh)]]
 [[FF7/WorldMap_Module/Script/Opcodes/339|339 hide active entity model]]
 [[FF7/WorldMap_Module/Script/Opcodes/33a|33a set active entity vertical speed 2]]
 [[FF7/WorldMap_Module/Script/Opcodes/33b|33b fade out?]]
 [[FF7/WorldMap_Module/Script/Opcodes/33c|33c set field entry point?]]
 [[FF7/WorldMap_Module/Script/Opcodes/33d|33d set field entry point2?]]
 [[FF7/WorldMap_Module/Script/Opcodes/33e|33e play music]]
 [[FF7/WorldMap_Module/Script/Opcodes/347|347 move active entity to entity by model id?]]
 [[FF7/WorldMap_Module/Script/Opcodes/348|348 fade in?]]
 [[FF7/WorldMap_Module/Script/Opcodes/349|349 set world progress]]
 [[FF7/WorldMap_Module/Script/Opcodes/34a|34a play 2d animation once]]
 [[FF7/WorldMap_Module/Script/Opcodes/34b|34b set chocobo type]]
 [[FF7/WorldMap_Module/Script/Opcodes/34c|34c set submarine color]]
 [[FF7/WorldMap_Module/Script/Opcodes/34d|34d start repeating 2d animation]]
 [[FF7/WorldMap_Module/Script/Opcodes/34e|34e stop repeating 2d animation]]
 [[FF7/WorldMap_Module/Script/Opcodes/34f|34f set active entity y position]]
 [[FF7/WorldMap_Module/Script/Opcodes/350|350 set meteor texture on/off]]
 [[FF7/WorldMap_Module/Script/Opcodes/351|351 set music volume]]
 [[FF7/WorldMap_Module/Script/Opcodes/352|352 shake camera on/off]]
 [[FF7/WorldMap_Module/Script/Opcodes/353|353 adjust current entity position outside vehicle]]
 [[FF7/WorldMap_Module/Script/Opcodes/354|354 set entity scripts on/off]]
 [[FF7/WorldMap_Module/Script/Opcodes/355|355 set battle countdown timer]]