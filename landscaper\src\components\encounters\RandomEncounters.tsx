import { useState } from 'react'
import { useEncountersState } from '@/hooks/useEncountersState'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { REGION_NAMES } from '@/lib/map-data'
import type { EncounterPair, EncounterSet } from '@/ff7/encwfile'

// Use only the first 16 regions for encounters
const ENCOUNTER_REGION_NAMES = Object.values(REGION_NAMES).slice(0, 16)

function EncounterEditor({
  encounter,
  onUpdate,
  label
}: {
  encounter: EncounterPair
  onUpdate: (updates: Partial<EncounterPair>) => void
  label: string
}) {
  return (
    <div className="flex items-center gap-2 p-2 border rounded">
      <Label className="w-24 text-sm">{label}</Label>
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">
          <Label className="text-xs">Battle ID:</Label>
          <Input
            type="number"
            min="0"
            max="1023"
            value={encounter.encounterId}
            onChange={(e) => onUpdate({ encounterId: parseInt(e.target.value) || 0 })}
            className="w-20 h-7 text-xs"
          />
        </div>
        <div className="flex items-center gap-1">
          <Label className="text-xs">Rate:</Label>
          <Input
            type="number"
            min="0"
            max="63"
            value={encounter.rate}
            onChange={(e) => onUpdate({ rate: parseInt(e.target.value) || 0 })}
            className="w-16 h-7 text-xs"
          />
        </div>
      </div>
    </div>
  )
}

function EncounterSetEditor({
  encounterSet,
  setIndex,
  onUpdateMeta,
  onUpdatePair
}: {
  encounterSet: EncounterSet
  setIndex: number
  onUpdateMeta: (updates: Partial<Pick<EncounterSet, 'active' | 'encounterRate'>>) => void
  onUpdatePair: (group: 'normal' | 'back' | 'side' | 'pincer' | 'chocobo', indexInGroup: number | null, updates: Partial<EncounterPair>) => void
}) {
  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Set {setIndex + 1}</CardTitle>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Label className="text-sm">Active:</Label>
              <Switch
                checked={encounterSet.active}
                onCheckedChange={(checked) => onUpdateMeta({ active: checked })}
              />
            </div>
            <div className="flex items-center gap-1">
              <Label className="text-sm">Rate:</Label>
              <Input
                type="number"
                min="0"
                max="255"
                value={encounterSet.encounterRate}
                onChange={(e) => onUpdateMeta({ encounterRate: parseInt(e.target.value) || 0 })}
                className="w-16 h-7 text-xs"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div>
            <h4 className="text-sm font-medium mb-2">Normal Encounters (6)</h4>
            <div className="grid grid-cols-2 gap-2">
              {encounterSet.normalEncounters.map((encounter, index) => (
                <EncounterEditor
                  key={index}
                  encounter={encounter}
                  onUpdate={(updates) => onUpdatePair('normal', index, updates)}
                  label={`Normal ${index + 1}`}
                />
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="text-sm font-medium mb-2">Back Attacks (2)</h4>
            <div className="grid grid-cols-2 gap-2">
              {encounterSet.backAttacks.map((encounter, index) => (
                <EncounterEditor
                  key={index}
                  encounter={encounter}
                  onUpdate={(updates) => onUpdatePair('back', index, updates)}
                  label={`Back ${index + 1}`}
                />
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="text-sm font-medium mb-2">Special Attacks</h4>
            <div className="grid grid-cols-2 gap-2">
              <EncounterEditor
                encounter={encounterSet.sideAttack}
                onUpdate={(updates) => onUpdatePair('side', null, updates)}
                label="Side Attack"
              />
              <EncounterEditor
                encounter={encounterSet.pincerAttack}
                onUpdate={(updates) => onUpdatePair('pincer', null, updates)}
                label="Both Sides"
              />
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="text-sm font-medium mb-2">Chocobo Encounters (4)</h4>
            <div className="grid grid-cols-2 gap-2">
              {encounterSet.chocoboEncounters.map((encounter, index) => (
                <EncounterEditor
                  key={index}
                  encounter={encounter}
                  onUpdate={(updates) => onUpdatePair('chocobo', index, updates)}
                  label={`Chocobo ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function RandomEncounters() {
  const [selectedRegion, setSelectedRegion] = useState(0)
  const [selectedSet, setSelectedSet] = useState(0)
  const { data, updateEncounterMeta, updateEncounterPair } = useEncountersState()

  if (!data) {
    return (
      <div className="flex-1 p-4">
        <div className="text-center text-muted-foreground">
          <h3 className="text-lg font-medium mb-2">Random Encounters</h3>
          <p>Loading encounter data...</p>
        </div>
      </div>
    )
  }

  const regionData = data.randomEncounters.regions[selectedRegion]

  return (
    <div className="flex-1 flex">
      {/* Region List */}
      <div className="w-48 border-r bg-muted/30 p-2">
        <h3 className="text-sm font-medium mb-2">Regions</h3>
        <div className="space-y-1">
          {ENCOUNTER_REGION_NAMES.map((name, index) => (
            <Button
              key={index}
              variant={selectedRegion === index ? "default" : "ghost"}
              size="sm"
              className="w-full justify-start text-left"
              onClick={() => setSelectedRegion(index)}
            >
              {name} ({index})
            </Button>
          ))}
        </div>
      </div>

      {/* Encounter Editor */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">Region {selectedRegion}: {ENCOUNTER_REGION_NAMES[selectedRegion]}</h3>
              <p className="text-sm text-muted-foreground">Configure encounter sets for this region</p>
            </div>
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">Set:</Label>
              <Select value={selectedSet.toString()} onValueChange={(value) => setSelectedSet(parseInt(value))}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Set 1</SelectItem>
                  <SelectItem value="1">Set 2</SelectItem>
                  <SelectItem value="2">Set 3</SelectItem>
                  <SelectItem value="3">Set 4</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Selected Set Editor */}
        <div className="space-y-4">
          <EncounterSetEditor
            encounterSet={regionData.sets[selectedSet]}
            setIndex={selectedSet}
            onUpdateMeta={(updates) => updateEncounterMeta(selectedRegion, selectedSet, updates)}
            onUpdatePair={(group, indexInGroup, updates) => updateEncounterPair(selectedRegion, selectedSet, group, indexInGroup, updates)}
          />
        </div>
      </div>
    </div>
  )
}
