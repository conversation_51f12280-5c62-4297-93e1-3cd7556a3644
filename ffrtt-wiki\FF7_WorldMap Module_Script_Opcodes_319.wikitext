* Opcode: '''0x319'''
* Name: set map options
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Options
| Bitmask with map options, see below
|}

==== Notes ====

Sets map and camera options, using a bitmask, with following possible values:

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Value
! style="background:rgb(204,204,204)" align="center" | Meaning
|-
| 0
| (default) mini map on, normal camera
|-
| 1
| camera low
|-
| 2
| show heading on the mini map
|-
| 4
| show big map
|-
| 8
| hide mini map
|}

NOTE: Setting the Options argument to 3 results in a weird, "close-up" camera with player hovering over ground and no ability to move.