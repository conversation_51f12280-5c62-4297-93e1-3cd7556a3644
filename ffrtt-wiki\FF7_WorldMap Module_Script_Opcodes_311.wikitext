* Opcode: '''0x311'''
* Name: set light mesh coordinates
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| X
| Mesh X coordinate
|-
| Stack
| Z
| Mesh Z coordinate
|}

==== Notes ====

Sets the active point's mesh coordinates, X is a number between 0 and 35 (for 9 MAP blocks with 4 meshes each) while Z is a number between 0 and 27 (for 7 MAP blocks with 4 meshes each).

World coordinates can be calculated as mesh coordinates << 13 + coordinates in mesh or mesh coordinates << 13 | coordinates in mesh (coordinates in mesh can be thought of as the lower 13 bits while mesh coordinates are the upper bits).