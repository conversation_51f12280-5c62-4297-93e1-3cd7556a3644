* Opcode: '''0x328'''
* Name: set active entity direction
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Direction
| Value between 0 and 255
|}

==== Notes ====

Sets the direction of movement of the current active entity. Does not make the entity face the direction of movement like [[FF7/WorldMap Module/Script/Opcodes/304|304]] does.