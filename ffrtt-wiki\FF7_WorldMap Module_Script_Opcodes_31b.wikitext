* Opcode: '''0x31b'''
* Name: noop
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|}

==== Notes ====

This instruction does absolutely nothing but it is still used for some reason. Perhaps a feature that was removed, the script suggests it had one parameter but since it doesn't consume anything that one parameter would've stayed on the stack forever. This is probably one of the bugs leading to the [[FF7/WorldMap Module/Script/Opcodes/100|reset stack]] hack.