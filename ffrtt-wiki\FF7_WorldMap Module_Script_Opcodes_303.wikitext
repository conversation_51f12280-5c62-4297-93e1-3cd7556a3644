* Opcode: '''0x303'''
* Name: set active entity movespeed
* Two-word opcode: No

==== Parameters ====

{| border="1" cellspacing="1" cellpadding="3" style="border: 1px solid black; border-collapse: collapse; text-align:center"
! style="background:rgb(204,204,204)" align="center" | Location
! style="background:rgb(204,204,204)" align="center" | Name
! style="background:rgb(204,204,204)" align="center" | Description
|-
| Stack
| Speed
| Value between 0 and 255
|}

==== Notes ====

Sets the movement speed of the current active entity. If the entity is not controlled by the player it will keep moving in its direction until speed is set to 0 or the player takes control of it.
Entities moving using this instruction will ignore the walkmesh.