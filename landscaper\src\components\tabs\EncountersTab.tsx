import { useEffect, useState } from 'react'
import { Button } from "@/components/ui/button"
import { RandomEncounters } from "@/components/encounters/RandomEncounters"
import { Yu<PERSON>ieEncounters } from "@/components/encounters/YuffieEncounters"
import { ChocoboEncounters } from "@/components/encounters/ChocoboEncounters"
import { useLgpState } from '@/hooks/useLgpState'
import { useEncountersState } from '@/hooks/useEncountersState'

type EncounterType = 'random' | 'yuffie' | 'chocobo'

export function EncountersTab() {
  const { opened, openedTime } = useLgpState()
  const { loadEncounters } = useEncountersState()
  const [selectedType, setSelectedType] = useState<EncounterType>('random')

  useEffect(() => {
    async function load() {
      if (!opened) return
      await loadEncounters()
    }
    load()
  }, [opened, openedTime])

  const renderContent = () => {
    switch (selectedType) {
      case 'random':
        return <RandomEncounters />
      case 'yuffie':
        return <YuffieEncounters />
      case 'chocobo':
        return <ChocoboEncounters />
      default:
        return <RandomEncounters />
    }
  }

  return (
    <div className="flex flex-col w-full min-h-0">
      {/* Top Controls Bar */}
      <div className="w-full bg-sidebar border-b border-slate-800/40 flex items-center justify-between gap-2 px-2 py-1">
        <div className="flex items-center gap-1.5">
          <Button
            variant={selectedType === 'random' ? 'default' : 'outline'}
            size="sm"
            className="h-6 text-xs px-3"
            onClick={() => setSelectedType('random')}
          >
            Random
          </Button>
          <Button
            variant={selectedType === 'yuffie' ? 'default' : 'outline'}
            size="sm"
            className="h-6 text-xs px-3"
            onClick={() => setSelectedType('yuffie')}
          >
            Yuffie
          </Button>
          <Button
            variant={selectedType === 'chocobo' ? 'default' : 'outline'}
            size="sm"
            className="h-6 text-xs px-3"
            onClick={() => setSelectedType('chocobo')}
          >
            Chocobo
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 min-h-0">
        {renderContent()}
      </div>
    </div>
  )
}