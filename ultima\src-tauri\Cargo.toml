[package]
name = "ff7-ultima"
version = "1.7.0"
description = "Real-time editor for Final Fantasy VII"
authors = ["m4v3r"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0.3", features = [] }

[dependencies]
ff7-lib = { path = "../../ff7-lib" }
tauri = { version = "2.1.0", features = ["unstable"] }
tauri-plugin-shell = "2.0.1"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
windows-hotkeys = "0.2.1"
tauri-plugin-store = "2"
tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
tauri-plugin-opener = "2.2.5"
log = "0.4"
fern = "0.6"
chrono = "0.4"
flate2 = "1.0" # Added for gzip decompression
byteorder = "1.5.0" # Added for binary data reading

[target."cfg(not(any(target_os = \"android\", target_os = \"ios\")))".dependencies]
tauri-plugin-global-shortcut = "2"
tauri-plugin-updater = "2"
tauri-plugin-window-state = "2"
